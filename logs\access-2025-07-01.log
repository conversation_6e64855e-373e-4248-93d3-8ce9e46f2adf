[2025-07-01T17:29:27.924Z] [ACCESS] GET / - 500 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"15ms","timestamp":"2025-07-01T17:29:27.923Z"}
[2025-07-01T17:30:49.810Z] [ACCESS] GET / - 200 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:30:49.809Z"}
[2025-07-01T17:30:49.836Z] [ACCESS] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:30:49.834Z"}
[2025-07-01T17:30:49.839Z] [ACCESS] GET /css/style.css - 200 - 8ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"8ms","timestamp":"2025-07-01T17:30:49.838Z"}
[2025-07-01T17:30:49.843Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:49.843Z"}
[2025-07-01T17:30:50.090Z] [ACCESS] GET /api/check-student-auth - 404 - 7ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:30:50.088Z"}
[2025-07-01T17:30:50.105Z] [ACCESS] GET /student/login - 404 - 4ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:50.103Z"}
[2025-07-01T17:30:51.329Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:51.328Z"}
[2025-07-01T17:30:51.337Z] [ACCESS] GET /api/auth/status - 404 - 4ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:51.337Z"}
[2025-07-01T17:30:51.344Z] [ACCESS] GET /api/auth/status - 404 - 3ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:51.343Z"}
[2025-07-01T17:30:52.142Z] [ACCESS] GET / - 200 - 795ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"795ms","timestamp":"2025-07-01T17:30:52.141Z"}
[2025-07-01T17:30:52.146Z] [ACCESS] GET /api/ratings - 404 - 2ms | {"method":"GET","url":"/api/ratings","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.146Z"}
[2025-07-01T17:30:52.152Z] [ACCESS] GET /favicon.ico - 404 - 3ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:52.151Z"}
[2025-07-01T17:30:52.156Z] [ACCESS] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.155Z"}
[2025-07-01T17:34:12.539Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:34:12.538Z"}
[2025-07-01T17:34:12.545Z] [ACCESS] GET /check - 200 - 1ms | {"method":"GET","url":"/check","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.544Z"}
[2025-07-01T17:34:12.548Z] [ACCESS] GET /session - 200 - 1ms | {"method":"GET","url":"/session","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.548Z"}
[2025-07-01T17:34:13.123Z] [ACCESS] GET / - 200 - 571ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"571ms","timestamp":"2025-07-01T17:34:13.122Z"}
[2025-07-01T17:34:13.204Z] [ACCESS] GET /leaderboard - 200 - 79ms | {"method":"GET","url":"/leaderboard","ip":"::1","statusCode":200,"responseTime":"79ms","timestamp":"2025-07-01T17:34:13.203Z"}
[2025-07-01T17:34:13.208Z] [ACCESS] GET /favicon.ico - 404 - 2ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.208Z"}
[2025-07-01T17:34:13.213Z] [ACCESS] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.212Z"}
[2025-07-01T17:34:39.624Z] [ACCESS] GET /student/login - 404 - 11ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"11ms","timestamp":"2025-07-01T17:34:39.623Z"}
[2025-07-01T17:34:44.608Z] [ACCESS] GET / - 304 - 10ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"10ms","timestamp":"2025-07-01T17:34:44.597Z"}
[2025-07-01T17:34:44.857Z] [ACCESS] GET /api/check-student-auth - 404 - 9ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"9ms","timestamp":"2025-07-01T17:34:44.856Z"}
[2025-07-01T17:34:44.884Z] [ACCESS] GET /student/login - 404 - 7ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:34:44.880Z"}
[2025-07-01T17:34:53.947Z] [ACCESS] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:34:53.947Z"}
[2025-07-01T17:34:54.093Z] [ACCESS] GET /api/check-student-auth - 404 - 3ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.093Z"}
[2025-07-01T17:34:54.100Z] [ACCESS] GET /student/login - 404 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.099Z"}
[2025-07-01T17:34:54.172Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.170Z"}
[2025-07-01T17:34:54.214Z] [ACCESS] GET /api/check-student-auth - 404 - 4ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:34:54.213Z"}
[2025-07-01T17:34:54.224Z] [ACCESS] GET /student/login - 404 - 3ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.223Z"}
[2025-07-01T17:37:15.633Z] [ACCESS] GET / - 304 - 11ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"11ms","timestamp":"2025-07-01T17:37:15.632Z"}
[2025-07-01T17:37:15.801Z] [ACCESS] GET / - 404 - 6ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T17:37:15.800Z"}
[2025-07-01T17:37:15.807Z] [ACCESS] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.807Z"}
[2025-07-01T17:37:15.848Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.847Z"}
[2025-07-01T17:37:15.892Z] [ACCESS] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:37:15.892Z"}
[2025-07-01T17:37:15.907Z] [ACCESS] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.906Z"}
[2025-07-01T17:37:15.943Z] [ACCESS] GET /js/device-id.js - 200 - 2ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.940Z"}
[2025-07-01T17:37:18.130Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:37:18.126Z"}
[2025-07-01T17:37:18.181Z] [ACCESS] GET / - 404 - 5ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"5ms","timestamp":"2025-07-01T17:37:18.180Z"}
[2025-07-01T17:37:18.199Z] [ACCESS] GET /student/login - 200 - 4ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:37:18.198Z"}
[2025-07-01T17:37:20.962Z] [ACCESS] POST / - 404 - 15ms | {"method":"POST","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"15ms","timestamp":"2025-07-01T17:37:20.961Z"}
[2025-07-01T17:37:23.093Z] [ACCESS] POST / - 404 - 4ms | {"method":"POST","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:37:23.092Z"}
[2025-07-01T17:37:30.730Z] [ACCESS] GET / - 304 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"4ms","timestamp":"2025-07-01T17:37:30.729Z"}
[2025-07-01T17:37:30.802Z] [ACCESS] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:37:30.792Z"}
[2025-07-01T17:37:30.806Z] [ACCESS] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:30.805Z"}
[2025-07-01T17:37:32.809Z] [ACCESS] GET /admin/login - 500 - 3ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","timestamp":"2025-07-01T17:37:32.808Z"}
[2025-07-01T17:38:25.826Z] [ACCESS] GET /admin/login - 500 - 24ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"24ms","timestamp":"2025-07-01T17:38:25.825Z"}
[2025-07-01T17:38:27.958Z] [ACCESS] GET /student/login - 200 - 6ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T17:38:27.957Z"}
[2025-07-01T17:38:30.615Z] [ACCESS] POST /api/student/login - 500 - 12ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"12ms","timestamp":"2025-07-01T17:38:30.614Z"}
[2025-07-01T17:38:33.949Z] [ACCESS] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:38:33.948Z"}
[2025-07-01T17:38:34.006Z] [ACCESS] GET /check-student-auth - 200 - 3ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:38:34.005Z"}
[2025-07-01T17:38:34.030Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:38:34.029Z"}
[2025-07-01T17:38:36.265Z] [ACCESS] GET /student/register - 200 - 3ms | {"method":"GET","url":"/student/register","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:38:36.262Z"}
[2025-07-01T17:38:40.124Z] [ACCESS] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:38:40.123Z"}
[2025-07-01T17:38:41.554Z] [ACCESS] GET /gallery - 200 - 5ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:38:41.553Z"}
[2025-07-01T17:38:41.589Z] [ACCESS] GET / - 404 - 8ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"8ms","timestamp":"2025-07-01T17:38:41.588Z"}
[2025-07-01T17:38:41.591Z] [ACCESS] GET /js/gallery.js - 200 - 5ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:38:41.591Z"}
[2025-07-01T17:38:41.603Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:38:41.602Z"}
[2025-07-01T17:38:41.670Z] [ACCESS] GET /student/login?redirect=%2Fgallery - 200 - 38ms | {"method":"GET","url":"/student/login?redirect=%2Fgallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"38ms","timestamp":"2025-07-01T17:38:41.658Z"}
[2025-07-01T17:39:10.385Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:10.385Z"}
[2025-07-01T17:39:10.418Z] [ACCESS] GET /css/style.css - 200 - 4ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:39:10.417Z"}
[2025-07-01T17:39:10.433Z] [ACCESS] GET /js/network-animation.js - 200 - 12ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:39:10.428Z"}
[2025-07-01T17:39:10.435Z] [ACCESS] GET /js/index.js - 200 - 15ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:39:10.434Z"}
[2025-07-01T17:39:10.836Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:10.836Z"}
[2025-07-01T17:39:10.846Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:10.845Z"}
[2025-07-01T17:39:10.865Z] [ACCESS] GET /js/device-id.js - 200 - 1ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:39:10.864Z"}
[2025-07-01T17:39:12.032Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:39:12.031Z"}
[2025-07-01T17:39:12.095Z] [ACCESS] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:39:12.094Z"}
[2025-07-01T17:39:12.130Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 1ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:39:12.129Z"}
[2025-07-01T17:39:14.344Z] [ACCESS] POST /api/student/login - 500 - 4ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"4ms","timestamp":"2025-07-01T17:39:14.341Z"}
[2025-07-01T17:39:16.608Z] [ACCESS] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:39:16.607Z"}
[2025-07-01T17:39:17.553Z] [ACCESS] GET /student/login - 200 - 309ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"309ms","timestamp":"2025-07-01T17:39:17.552Z"}
[2025-07-01T17:39:21.015Z] [ACCESS] GET /admin/login - 200 - 32ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"32ms","timestamp":"2025-07-01T17:39:21.014Z"}
[2025-07-01T17:39:23.350Z] [ACCESS] GET / - 200 - 33ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"33ms","timestamp":"2025-07-01T17:39:23.349Z"}
[2025-07-01T17:39:23.578Z] [ACCESS] GET /check-student-auth - 200 - 32ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"32ms","timestamp":"2025-07-01T17:39:23.577Z"}
[2025-07-01T17:39:23.728Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 29ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"29ms","timestamp":"2025-07-01T17:39:23.727Z"}
[2025-07-01T17:39:35.718Z] [ACCESS] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:35.717Z"}
[2025-07-01T17:39:40.897Z] [ACCESS] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:40.896Z"}
[2025-07-01T17:39:40.939Z] [ACCESS] GET /css/style.css - 200 - 2ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:40.936Z"}
[2025-07-01T17:39:40.943Z] [ACCESS] GET /js/device-id.js - 200 - 2ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:40.942Z"}
[2025-07-01T17:39:42.639Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:42.638Z"}
[2025-07-01T17:39:42.670Z] [ACCESS] GET /js/network-animation.js - 200 - 2ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:42.669Z"}
[2025-07-01T17:39:42.672Z] [ACCESS] GET /js/index.js - 200 - 3ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:39:42.671Z"}
[2025-07-01T17:39:42.725Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:39:42.719Z"}
[2025-07-01T17:39:42.777Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 4ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:39:42.746Z"}
[2025-07-01T17:39:47.726Z] [ACCESS] POST /api/student/login - 500 - 3ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","timestamp":"2025-07-01T17:39:47.725Z"}
[2025-07-01T17:40:15.326Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 14ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-01T17:40:15.325Z"}
[2025-07-01T17:40:17.446Z] [ACCESS] POST /api/student/login - 500 - 16ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"16ms","timestamp":"2025-07-01T17:40:17.446Z"}
[2025-07-01T17:41:37.458Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 13ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"13ms","timestamp":"2025-07-01T17:41:37.458Z"}
[2025-07-01T17:41:41.129Z] [ACCESS] POST /student/login - 200 - 1106ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1106ms","timestamp":"2025-07-01T17:41:41.128Z"}
[2025-07-01T17:41:41.714Z] [ACCESS] GET / - 304 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"64ms","timestamp":"2025-07-01T17:41:41.713Z"}
[2025-07-01T17:41:41.820Z] [ACCESS] GET /check-student-auth - 200 - 61ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T17:41:41.819Z"}
[2025-07-01T17:41:41.895Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 59ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T17:41:41.895Z"}
[2025-07-01T17:41:43.738Z] [ACCESS] GET /student/login - 200 - 8ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"8ms","timestamp":"2025-07-01T17:41:43.732Z"}
[2025-07-01T17:41:48.858Z] [ACCESS] POST /student/login - 400 - 66ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"66ms","timestamp":"2025-07-01T17:41:48.857Z"}
[2025-07-01T17:41:50.640Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 62ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T17:41:50.639Z"}
[2025-07-01T17:41:51.384Z] [ACCESS] GET / - 304 - 61ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"61ms","timestamp":"2025-07-01T17:41:51.383Z"}
[2025-07-01T17:41:51.624Z] [ACCESS] GET /check-student-auth - 200 - 197ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"197ms","timestamp":"2025-07-01T17:41:51.623Z"}
[2025-07-01T17:41:51.710Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 61ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T17:41:51.704Z"}
[2025-07-01T17:41:54.121Z] [ACCESS] GET /js/gallery.js - 200 - 2ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:41:54.120Z"}
[2025-07-01T17:41:54.125Z] [ACCESS] GET /gallery - 200 - 60ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"60ms","timestamp":"2025-07-01T17:41:54.125Z"}
[2025-07-01T17:41:54.356Z] [ACCESS] GET / - 404 - 239ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"239ms","timestamp":"2025-07-01T17:41:54.355Z"}
[2025-07-01T17:41:54.399Z] [ACCESS] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-01T17:41:54.398Z"}
[2025-07-01T17:41:54.474Z] [ACCESS] GET /student/login?redirect=%2Fgallery - 200 - 62ms | {"method":"GET","url":"/student/login?redirect=%2Fgallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T17:41:54.473Z"}
[2025-07-01T17:41:55.905Z] [ACCESS] GET /admin/login - 200 - 60ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"60ms","timestamp":"2025-07-01T17:41:55.904Z"}
[2025-07-01T17:42:00.145Z] [ACCESS] POST /admin/login - 400 - 62ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"62ms","timestamp":"2025-07-01T17:42:00.144Z"}
[2025-07-01T17:42:02.429Z] [ACCESS] GET / - 304 - 61ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"61ms","timestamp":"2025-07-01T17:42:02.428Z"}
[2025-07-01T17:42:02.527Z] [ACCESS] GET /check-student-auth - 200 - 59ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T17:42:02.526Z"}
[2025-07-01T17:42:02.602Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 61ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T17:42:02.601Z"}
[2025-07-01T17:42:33.825Z] [ACCESS] GET / - 304 - 268ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"268ms","timestamp":"2025-07-01T17:42:33.823Z"}
[2025-07-01T17:42:34.125Z] [ACCESS] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T17:42:34.124Z"}
[2025-07-01T17:42:34.204Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 69ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T17:42:34.203Z"}
[2025-07-01T17:42:36.750Z] [ACCESS] POST /student/login - 400 - 66ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"66ms","timestamp":"2025-07-01T17:42:36.750Z"}
[2025-07-01T17:42:40.677Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 67ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:42:40.676Z"}
[2025-07-01T17:42:47.069Z] [ACCESS] GET / - 304 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"68ms","timestamp":"2025-07-01T17:42:47.068Z"}
[2025-07-01T17:42:47.177Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T17:42:47.177Z"}
[2025-07-01T17:42:47.263Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 72ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T17:42:47.262Z"}
[2025-07-01T17:42:56.281Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:42:56.281Z"}
[2025-07-01T17:42:56.632Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:42:56.631Z"}
[2025-07-01T17:42:56.745Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:42:56.744Z"}
[2025-07-01T17:42:58.170Z] [ACCESS] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:42:58.170Z"}
[2025-07-01T17:43:03.863Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:43:03.863Z"}
[2025-07-01T17:43:03.907Z] [ACCESS] GET / - 404 - 7ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:43:03.906Z"}
[2025-07-01T17:43:06.308Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:43:06.307Z"}
[2025-07-01T17:43:06.365Z] [ACCESS] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:43:06.364Z"}
[2025-07-01T17:43:06.887Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:43:06.886Z"}
[2025-07-01T17:43:07.044Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 1ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:43:07.043Z"}
[2025-07-01T17:43:07.108Z] [ACCESS] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:43:07.106Z"}
[2025-07-01T17:43:52.505Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 16ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"16ms","timestamp":"2025-07-01T17:43:52.504Z"}
[2025-07-01T17:43:54.378Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:43:54.377Z"}
[2025-07-01T17:43:54.606Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:43:54.605Z"}
[2025-07-01T17:43:54.741Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:43:54.741Z"}
[2025-07-01T17:44:01.495Z] [ACCESS] GET /student/login - 200 - 5ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:44:01.494Z"}
[2025-07-01T17:44:10.299Z] [ACCESS] GET / - 200 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:44:10.297Z"}
[2025-07-01T17:44:10.346Z] [ACCESS] GET /css/style.css - 200 - 4ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:44:10.345Z"}
[2025-07-01T17:44:10.349Z] [ACCESS] GET /js/network-animation.js - 200 - 5ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:44:10.348Z"}
[2025-07-01T17:44:10.415Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:44:10.414Z"}
[2025-07-01T17:44:10.575Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:44:10.575Z"}
[2025-07-01T17:44:10.588Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 5ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:44:10.586Z"}
[2025-07-01T17:44:10.626Z] [ACCESS] GET /js/device-id.js - 200 - 4ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:44:10.624Z"}
[2025-07-01T17:44:12.261Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:44:12.260Z"}
[2025-07-01T17:44:12.311Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:44:12.310Z"}
[2025-07-01T17:44:12.334Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 1ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:44:12.333Z"}
[2025-07-01T17:44:17.705Z] [ACCESS] POST /student/login - 200 - 1126ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1126ms","timestamp":"2025-07-01T17:44:17.704Z"}
[2025-07-01T17:44:18.293Z] [ACCESS] GET / - 304 - 71ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"71ms","timestamp":"2025-07-01T17:44:18.292Z"}
[2025-07-01T17:44:18.404Z] [ACCESS] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T17:44:18.403Z"}
[2025-07-01T17:44:18.486Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 73ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"73ms","timestamp":"2025-07-01T17:44:18.485Z"}
[2025-07-01T17:51:54.583Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 364ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"364ms","timestamp":"2025-07-01T17:51:54.582Z"}
[2025-07-01T17:51:56.816Z] [ACCESS] POST /student/login - 400 - 94ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"94ms","timestamp":"2025-07-01T17:51:56.815Z"}
[2025-07-01T17:51:59.192Z] [ACCESS] GET /student/login?redirect=%2F - 200 - 73ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"73ms","timestamp":"2025-07-01T17:51:59.191Z"}
[2025-07-01T17:51:59.908Z] [ACCESS] GET /images/lesson2.jpg - 200 - 2ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:51:59.907Z"}
[2025-07-01T17:51:59.922Z] [ACCESS] GET /images/lesson4.jpg - 200 - 7ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:51:59.921Z"}
[2025-07-01T17:51:59.924Z] [ACCESS] GET /js/landing.js - 200 - 7ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:51:59.923Z"}
[2025-07-01T17:51:59.926Z] [ACCESS] GET /images/lesson3.jpg - 200 - 12ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:51:59.925Z"}
[2025-07-01T17:51:59.929Z] [ACCESS] GET / - 200 - 86ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"86ms","timestamp":"2025-07-01T17:51:59.928Z"}
[2025-07-01T17:51:59.930Z] [ACCESS] GET /images/lesson1.jpg - 200 - 30ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"30ms","timestamp":"2025-07-01T17:51:59.929Z"}
[2025-07-01T17:52:04.820Z] [ACCESS] GET /lessons - 200 - 68ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T17:52:04.819Z"}
[2025-07-01T17:52:04.894Z] [ACCESS] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T17:52:04.893Z"}
[2025-07-01T17:52:04.976Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 72ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T17:52:04.976Z"}
[2025-07-01T17:52:06.273Z] [ACCESS] GET / - 200 - 7ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:52:06.267Z"}
[2025-07-01T17:52:10.278Z] [ACCESS] GET /lessons - 200 - 4ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:52:10.276Z"}
[2025-07-01T17:52:10.891Z] [ACCESS] GET /check-student-auth - 200 - 3ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:52:10.890Z"}
[2025-07-01T17:52:11.107Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:52:11.104Z"}
[2025-07-01T17:52:14.369Z] [ACCESS] POST /student/login - 400 - 70ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"70ms","timestamp":"2025-07-01T17:52:14.368Z"}
[2025-07-01T17:52:18.777Z] [ACCESS] GET / - 304 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T17:52:18.776Z"}
[2025-07-01T17:52:20.089Z] [ACCESS] GET /lessons - 304 - 69ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T17:52:20.088Z"}
[2025-07-01T17:52:20.208Z] [ACCESS] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-01T17:52:20.207Z"}
[2025-07-01T17:52:20.293Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 69ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T17:52:20.293Z"}
[2025-07-01T17:52:32.730Z] [ACCESS] GET /js/gallery.js - 200 - 3ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:52:32.729Z"}
[2025-07-01T17:52:32.737Z] [ACCESS] GET /gallery - 200 - 542ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"542ms","timestamp":"2025-07-01T17:52:32.737Z"}
[2025-07-01T17:52:33.016Z] [ACCESS] GET / - 404 - 290ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"290ms","timestamp":"2025-07-01T17:52:33.015Z"}
[2025-07-01T17:52:33.055Z] [ACCESS] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:52:33.054Z"}
[2025-07-01T17:52:33.125Z] [ACCESS] GET / - 404 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"67ms","timestamp":"2025-07-01T17:52:33.124Z"}
[2025-07-01T17:52:55.805Z] [ACCESS] GET / - 304 - 232ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"232ms","timestamp":"2025-07-01T17:52:55.804Z"}
[2025-07-01T17:52:56.831Z] [ACCESS] GET /quizgame - 200 - 76ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-01T17:52:56.830Z"}
[2025-07-01T17:52:57.035Z] [ACCESS] GET /js/quizgame.js - 200 - 1ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:52:57.034Z"}
[2025-07-01T17:52:57.054Z] [ACCESS] GET /audio/5sec_1.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T17:52:57.053Z"}
[2025-07-01T17:52:57.061Z] [ACCESS] GET /audio/30sec_1.mp3 - 206 - 20ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"20ms","timestamp":"2025-07-01T17:52:57.060Z"}
[2025-07-01T17:52:57.063Z] [ACCESS] GET /audio/5sec_2.mp3 - 206 - 6ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"6ms","timestamp":"2025-07-01T17:52:57.062Z"}
[2025-07-01T17:52:57.066Z] [ACCESS] GET /audio/5sec_3.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-01T17:52:57.065Z"}
[2025-07-01T17:52:57.068Z] [ACCESS] GET /audio/30sec_3.mp3 - 206 - 24ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"24ms","timestamp":"2025-07-01T17:52:57.067Z"}
[2025-07-01T17:52:57.069Z] [ACCESS] GET /audio/30sec_2.mp3 - 206 - 26ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"26ms","timestamp":"2025-07-01T17:52:57.068Z"}
[2025-07-01T17:52:57.079Z] [ACCESS] GET /audio/correct_1.mp3 - 206 - 5ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"5ms","timestamp":"2025-07-01T17:52:57.078Z"}
[2025-07-01T17:52:57.082Z] [ACCESS] GET /audio/correct_2.mp3 - 206 - 7ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"7ms","timestamp":"2025-07-01T17:52:57.081Z"}
[2025-07-01T17:52:57.084Z] [ACCESS] GET /audio/correct_3.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-01T17:52:57.083Z"}
[2025-07-01T17:52:57.085Z] [ACCESS] GET /audio/correct_4.mp3 - 206 - 10ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"10ms","timestamp":"2025-07-01T17:52:57.085Z"}
[2025-07-01T17:52:57.087Z] [ACCESS] GET /audio/correct_5.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T17:52:57.087Z"}
[2025-07-01T17:52:57.089Z] [ACCESS] GET /audio/incorrect.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-01T17:52:57.088Z"}
[2025-07-01T17:52:57.091Z] [ACCESS] GET /audio/points.mp3 - 206 - 4ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"4ms","timestamp":"2025-07-01T17:52:57.090Z"}
[2025-07-01T17:53:00.313Z] [ACCESS] GET /leaderboard - 200 - 98ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"98ms","timestamp":"2025-07-01T17:53:00.313Z"}
[2025-07-01T17:53:00.445Z] [ACCESS] GET /?page=1&filter=all - 404 - 118ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"118ms","timestamp":"2025-07-01T17:53:00.444Z"}
[2025-07-01T17:53:02.807Z] [ACCESS] GET /?page=1&filter=month - 404 - 61ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"61ms","timestamp":"2025-07-01T17:53:02.807Z"}
[2025-07-01T17:53:03.433Z] [ACCESS] GET /?page=1&filter=week - 404 - 61ms | {"method":"GET","url":"/?page=1&filter=week","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"61ms","timestamp":"2025-07-01T17:53:03.432Z"}
[2025-07-01T17:53:04.062Z] [ACCESS] GET /?page=1&filter=all - 404 - 78ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"78ms","timestamp":"2025-07-01T17:53:04.059Z"}
[2025-07-01T17:53:36.871Z] [ACCESS] GET / - 304 - 313ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"313ms","timestamp":"2025-07-01T17:53:36.869Z"}
[2025-07-01T17:54:11.952Z] [ACCESS] GET /lessons?v=1 - 200 - 16ms | {"method":"GET","url":"/lessons?v=1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"16ms","timestamp":"2025-07-01T17:54:11.952Z"}
[2025-07-01T17:54:12.501Z] [ACCESS] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:54:12.500Z"}
[2025-07-01T17:54:12.647Z] [ACCESS] GET /student/login?redirect=%2Flessons%3Fv%3D1 - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons%3Fv%3D1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:12.646Z"}
[2025-07-01T17:54:18.165Z] [ACCESS] GET / - 304 - 323ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"323ms","timestamp":"2025-07-01T17:54:18.164Z"}
[2025-07-01T17:54:19.192Z] [ACCESS] GET /lessons - 304 - 62ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"62ms","timestamp":"2025-07-01T17:54:19.191Z"}
[2025-07-01T17:54:19.296Z] [ACCESS] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-01T17:54:19.294Z"}
[2025-07-01T17:54:19.372Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 67ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:54:19.371Z"}
[2025-07-01T17:54:21.639Z] [ACCESS] POST /student/login - 400 - 83ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"83ms","timestamp":"2025-07-01T17:54:21.637Z"}
[2025-07-01T17:54:23.001Z] [ACCESS] POST /student/login - 400 - 67ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"67ms","timestamp":"2025-07-01T17:54:23.001Z"}
[2025-07-01T17:54:28.467Z] [ACCESS] GET /student/login?redirect=%2Flessons - 200 - 67ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:54:28.466Z"}
[2025-07-01T17:54:31.411Z] [ACCESS] POST /student/login - 400 - 62ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"62ms","timestamp":"2025-07-01T17:54:31.410Z"}
[2025-07-01T17:54:44.705Z] [ACCESS] GET / - 200 - 4ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:54:44.704Z"}
[2025-07-01T17:54:44.772Z] [ACCESS] GET /images/lesson1.jpg - 200 - 7ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:54:44.771Z"}
[2025-07-01T17:54:44.776Z] [ACCESS] GET /images/lesson2.jpg - 200 - 10ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"10ms","timestamp":"2025-07-01T17:54:44.776Z"}
[2025-07-01T17:54:44.780Z] [ACCESS] GET /js/landing.js - 200 - 12ms | {"method":"GET","url":"/js/landing.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:54:44.780Z"}
[2025-07-01T17:54:44.782Z] [ACCESS] GET /js/network-animation.js - 304 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:54:44.781Z"}
[2025-07-01T17:54:44.784Z] [ACCESS] GET /images/lesson3.jpg - 200 - 15ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:54:44.784Z"}
[2025-07-01T17:54:44.787Z] [ACCESS] GET /css/style.css - 200 - 12ms | {"method":"GET","url":"/css/style.css","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:54:44.786Z"}
[2025-07-01T17:54:44.883Z] [ACCESS] GET /images/lesson4.jpg - 200 - 1ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:44.883Z"}
[2025-07-01T17:54:46.375Z] [ACCESS] GET /lessons - 200 - 2ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:46.374Z"}
[2025-07-01T17:54:46.411Z] [ACCESS] GET /js/index.js - 200 - 1ms | {"method":"GET","url":"/js/index.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:46.410Z"}
[2025-07-01T17:54:46.446Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:46.445Z"}
[2025-07-01T17:54:46.476Z] [ACCESS] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:54:46.475Z"}
[2025-07-01T17:54:47.070Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 591ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"591ms","timestamp":"2025-07-01T17:54:47.069Z"}
[2025-07-01T17:54:50.308Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:50.308Z"}
[2025-07-01T17:54:51.394Z] [ACCESS] GET /quizgame - 200 - 2ms | {"method":"GET","url":"/quizgame","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:51.393Z"}
[2025-07-01T17:54:51.427Z] [ACCESS] GET /js/quizgame.js - 200 - 2ms | {"method":"GET","url":"/js/quizgame.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:51.427Z"}
[2025-07-01T17:54:51.636Z] [ACCESS] GET /audio/5sec_1.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T17:54:51.635Z"}
[2025-07-01T17:54:51.646Z] [ACCESS] GET /audio/5sec_2.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-01T17:54:51.638Z"}
[2025-07-01T17:54:51.662Z] [ACCESS] GET /audio/5sec_3.mp3 - 206 - 29ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"29ms","timestamp":"2025-07-01T17:54:51.655Z"}
[2025-07-01T17:54:51.675Z] [ACCESS] GET /audio/30sec_1.mp3 - 206 - 49ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"49ms","timestamp":"2025-07-01T17:54:51.664Z"}
[2025-07-01T17:54:51.748Z] [ACCESS] GET /audio/30sec_2.mp3 - 206 - 128ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"128ms","timestamp":"2025-07-01T17:54:51.746Z"}
[2025-07-01T17:54:51.756Z] [ACCESS] GET /audio/correct_1.mp3 - 206 - 103ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"103ms","timestamp":"2025-07-01T17:54:51.755Z"}
[2025-07-01T17:54:51.758Z] [ACCESS] GET /audio/correct_2.mp3 - 206 - 104ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"104ms","timestamp":"2025-07-01T17:54:51.757Z"}
[2025-07-01T17:54:51.779Z] [ACCESS] GET /audio/30sec_3.mp3 - 206 - 141ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"141ms","timestamp":"2025-07-01T17:54:51.762Z"}
[2025-07-01T17:54:51.788Z] [ACCESS] GET /audio/correct_3.mp3 - 206 - 64ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"64ms","timestamp":"2025-07-01T17:54:51.786Z"}
[2025-07-01T17:54:51.837Z] [ACCESS] GET /audio/correct_4.mp3 - 206 - 83ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"83ms","timestamp":"2025-07-01T17:54:51.829Z"}
[2025-07-01T17:54:51.848Z] [ACCESS] GET /audio/incorrect.mp3 - 206 - 55ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"55ms","timestamp":"2025-07-01T17:54:51.846Z"}
[2025-07-01T17:54:51.874Z] [ACCESS] GET /audio/correct_5.mp3 - 206 - 74ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"74ms","timestamp":"2025-07-01T17:54:51.863Z"}
[2025-07-01T17:54:51.878Z] [ACCESS] GET /audio/points.mp3 - 206 - 82ms | {"method":"GET","url":"/audio/points.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"82ms","timestamp":"2025-07-01T17:54:51.875Z"}
[2025-07-01T17:54:53.145Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:53.144Z"}
[2025-07-01T17:54:53.946Z] [ACCESS] GET /lessons - 304 - 4ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"4ms","timestamp":"2025-07-01T17:54:53.945Z"}
[2025-07-01T17:54:54.005Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:54.004Z"}
[2025-07-01T17:54:54.015Z] [ACCESS] GET / - 404 - 6ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T17:54:54.014Z"}
[2025-07-01T17:54:55.632Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:55.631Z"}
[2025-07-01T17:54:56.468Z] [ACCESS] GET /lessons - 304 - 2ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:54:56.467Z"}
[2025-07-01T17:54:56.518Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:56.517Z"}
[2025-07-01T17:54:56.532Z] [ACCESS] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:54:56.531Z"}
[2025-07-01T17:54:58.524Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:58.523Z"}
[2025-07-01T17:54:59.174Z] [ACCESS] GET /lessons - 304 - 1ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:59.173Z"}
[2025-07-01T17:54:59.226Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:59.225Z"}
[2025-07-01T17:54:59.239Z] [ACCESS] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:54:59.238Z"}
[2025-07-01T17:55:00.368Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:55:00.366Z"}
[2025-07-01T17:55:02.501Z] [ACCESS] GET /lessons - 304 - 2ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:55:02.500Z"}
[2025-07-01T17:55:02.547Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:55:02.546Z"}
[2025-07-01T17:55:02.556Z] [ACCESS] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:55:02.555Z"}
[2025-07-01T17:55:04.956Z] [ACCESS] GET /admin/login - 200 - 2ms | {"method":"GET","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:55:04.956Z"}
[2025-07-01T17:55:05.972Z] [ACCESS] GET /lessons?debug=1 - 200 - 5ms | {"method":"GET","url":"/lessons?debug=1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:55:05.966Z"}
[2025-07-01T17:55:06.709Z] [ACCESS] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:55:06.707Z"}
[2025-07-01T17:55:06.896Z] [ACCESS] GET /student/login?redirect=%2Flessons%3Fdebug%3D1 - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons%3Fdebug%3D1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:55:06.896Z"}
[2025-07-01T17:55:17.483Z] [ACCESS] POST /admin/login - 200 - 280ms | {"method":"POST","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"280ms","timestamp":"2025-07-01T17:55:17.482Z"}
[2025-07-01T17:55:18.064Z] [ACCESS] GET /admin - 500 - 64ms | {"method":"GET","url":"/admin","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":500,"responseTime":"64ms","timestamp":"2025-07-01T17:55:18.062Z"}
[2025-07-01T17:55:18.118Z] [ACCESS] GET / - 404 - 2ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:55:18.117Z"}
